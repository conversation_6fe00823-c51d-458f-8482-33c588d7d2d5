<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Advertisement;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class SellerController extends Controller
{
    /**
     * Display the specified seller's profile
     */
    public function show(User $seller): Response
    {
        // Get seller's published advertisements
        $advertisements = Advertisement::with(['vehicle.brand', 'vehicle.category', 'featuredImage'])
            ->where('user_id', $seller->id)
            ->where('status', 'published')
            ->latest()
            ->paginate(12);

        // Get seller statistics
        $stats = [
            'total_ads' => Advertisement::where('user_id', $seller->id)
                ->where('status', 'published')
                ->count(),
            'active_ads' => Advertisement::where('user_id', $seller->id)
                ->where('status', 'published')
                ->count(),
            'member_since' => $seller->created_at->format('Y'),
            'avg_response_time' => '2 horas', // This would be calculated from actual data
            'rating' => 4.5, // This would come from a ratings system
            'total_reviews' => 23, // This would come from a reviews system
            'total_sales' => 15, // This would come from a sales tracking system
            'verified_seller' => true, // This would be a field in the users table
        ];

        // Get recent reviews (if you have a review system)
        $reviews = []; // This would be populated from a reviews table

        // Get seller's specialties (most common categories)
        $specialties = Advertisement::selectRaw('categories.name, COUNT(*) as count')
            ->join('vehicles', 'advertisements.vehicle_id', '=', 'vehicles.id')
            ->join('categories', 'vehicles.category_id', '=', 'categories.id')
            ->where('advertisements.user_id', $seller->id)
            ->where('advertisements.status', 'published')
            ->groupBy('categories.id', 'categories.name')
            ->orderByDesc('count')
            ->limit(3)
            ->get();

        return Inertia::render('Sellers/Show', [
            'seller' => $seller->only(['id', 'name', 'avatar', 'created_at']),
            'advertisements' => $advertisements,
            'stats' => $stats,
            'reviews' => $reviews,
            'specialties' => $specialties,
        ]);
    }

    /**
     * Display seller's advertisements with filters
     */
    public function advertisements(Request $request, User $seller): Response
    {
        $query = Advertisement::with(['vehicle.brand', 'vehicle.category', 'featuredImage'])
            ->where('user_id', $seller->id)
            ->where('status', 'published');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('vehicle', function($vehicleQuery) use ($search) {
                      $vehicleQuery->where('model', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('category_id')) {
            $query->whereHas('vehicle', function($vehicleQuery) use ($request) {
                $vehicleQuery->where('category_id', $request->input('category_id'));
            });
        }

        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->input('min_price'));
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->input('max_price'));
        }

        // Sorting
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        
        $validSortFields = ['created_at', 'price', 'title'];
        if (in_array($sortBy, $validSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $advertisements = $query->paginate(12)->withQueryString();

        return Inertia::render('Sellers/Advertisements', [
            'seller' => $seller->only(['id', 'name', 'avatar', 'created_at']),
            'advertisements' => $advertisements,
            'filters' => $request->all(),
        ]);
    }

    /**
     * Contact seller
     */
    public function contact(Request $request, User $seller)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'required|string|max:1000',
            'subject' => 'required|string|max:255',
        ]);

        // Here you would typically send an email or create a message record
        // For now, we'll just return a success response
        
        return response()->json([
            'message' => 'Mensagem enviada com sucesso! O vendedor entrará em contato em breve.'
        ]);
    }

    /**
     * Report seller
     */
    public function report(Request $request, User $seller)
    {
        $request->validate([
            'reason' => 'required|string|in:spam,inappropriate,fake,harassment,fraud,other',
            'description' => 'nullable|string|max:500',
        ]);

        // Here you would typically create a report record
        // For now, we'll just return a success response
        
        return response()->json([
            'message' => 'Denúncia enviada com sucesso! Nossa equipe irá analisar.'
        ]);
    }

    /**
     * Follow/unfollow seller
     */
    public function follow(Request $request, User $seller)
    {
        if (!auth()->check()) {
            return response()->json(['message' => 'Você precisa estar logado para seguir vendedores.'], 401);
        }

        $user = auth()->user();
        
        // Check if already following
        if ($user->followedSellers()->where('seller_id', $seller->id)->exists()) {
            // Unfollow
            $user->followedSellers()->detach($seller->id);
            return response()->json(['following' => false, 'message' => 'Você não está mais seguindo este vendedor']);
        } else {
            // Follow
            $user->followedSellers()->attach($seller->id);
            return response()->json(['following' => true, 'message' => 'Você está seguindo este vendedor']);
        }
    }
}
