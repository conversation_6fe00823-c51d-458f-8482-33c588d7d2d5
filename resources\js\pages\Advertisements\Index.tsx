import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    <PERSON>alogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import MainLayout from '@/layouts/MainLayout';
import { PageProps } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import {
    Filter,
    Grid,
    Heart,
    List,
    MapPin,
    MessageCircle,
    Search,
    User,
} from 'lucide-react';
import { useState } from 'react';

interface Advertisement {
    id: number;
    title: string;
    description: string;
    price: number;
    location: string;
    created_at: string;
    is_featured: boolean;
    featured_image?: {
        url: string;
        alt: string;
    };
    vehicle: {
        id: number;
        model: string;
        year: number;
        mileage: number;
        fuel_type: string;
        transmission: string;
        brand: {
            id: number;
            name: string;
        };
        category: {
            id: number;
            name: string;
        };
    };
    user: {
        id: number;
        name: string;
    };
}

interface AdvertisementsIndexProps extends PageProps {
    advertisements: {
        data: Advertisement[];
        links: any[];
        meta: any;
    };
    filters: Record<string, any>;
}

export default function AdvertisementsIndex({
    advertisements,
    filters,
}: AdvertisementsIndexProps) {
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [localFilters, setLocalFilters] = useState({
        search: filters.search || '',
        sort_by: filters.sort_by || 'created_at',
        sort_order: filters.sort_order || 'desc',
    });

    // Chat functionality
    const [selectedAd, setSelectedAd] = useState<Advertisement | null>(null);
    const { data, setData, post, processing, reset } = useForm({
        message: '',
    });

    const handleChatSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!selectedAd || !data.message.trim()) return;

        post(`/anuncios/${selectedAd.id}/contato`, {
            onSuccess: () => {
                reset();
                setSelectedAd(null);
            },
        });
    };

    const handleFilterChange = (key: string, value: any) => {
        setLocalFilters((prev) => ({ ...prev, [key]: value }));
    };

    const applyFilters = () => {
        const cleanFilters = Object.fromEntries(
            Object.entries(localFilters).filter(
                ([_, value]) =>
                    value !== '' && value !== null && value !== undefined,
            ),
        );

        router.get('/anuncios', cleanFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(price);
    };

    const formatMileage = (mileage: number) => {
        return new Intl.NumberFormat('pt-BR').format(mileage) + ' km';
    };

    const formatDate = (date: string) => {
        return new Date(date).toLocaleDateString('pt-BR');
    };

    return (
        <MainLayout>
            <Head title="Anúncios de Veículos" />

            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <div className="border-b bg-white shadow-sm">
                    <div className="container mx-auto px-4 py-6">
                        <div className="flex flex-col gap-4 lg:flex-row">
                            {/* Barra de pesquisa */}
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute top-1/2 left-3 h-5 w-5 -translate-y-1/2 transform text-gray-400" />
                                    <Input
                                        placeholder="Buscar anúncios..."
                                        value={localFilters.search}
                                        onChange={(e) =>
                                            handleFilterChange(
                                                'search',
                                                e.target.value,
                                            )
                                        }
                                        onKeyPress={(e) =>
                                            e.key === 'Enter' && applyFilters()
                                        }
                                        className="h-12 pl-10"
                                    />
                                </div>
                            </div>

                            {/* Botões de ação */}
                            <div className="flex gap-2">
                                <Button
                                    onClick={applyFilters}
                                    className="h-12 px-6"
                                >
                                    <Search className="mr-2 h-4 w-4" />
                                    Buscar
                                </Button>

                                <Link href="/pesquisar">
                                    <Button
                                        variant="outline"
                                        className="h-12 px-4"
                                    >
                                        <Filter className="mr-2 h-4 w-4" />
                                        Filtros avançados
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="container mx-auto px-4 py-6">
                    {/* Barra de resultados e ordenação */}
                    <div className="mb-6 flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
                        <div>
                            <h1 className="text-2xl font-bold">
                                {advertisements?.meta?.total || 0} anúncios
                                encontrados
                            </h1>
                            {filters.search && (
                                <p className="text-gray-600">
                                    Resultados para "{filters.search}"
                                </p>
                            )}
                        </div>

                        <div className="flex items-center gap-2">
                            {/* Ordenação */}
                            <Select
                                value={`${localFilters.sort_by}-${localFilters.sort_order}`}
                                onValueChange={(value) => {
                                    const [sortBy, sortOrder] =
                                        value.split('-');
                                    handleFilterChange('sort_by', sortBy);
                                    handleFilterChange('sort_order', sortOrder);
                                    applyFilters();
                                }}
                            >
                                <SelectTrigger className="w-48">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="created_at-desc">
                                        Mais recentes
                                    </SelectItem>
                                    <SelectItem value="created_at-asc">
                                        Mais antigos
                                    </SelectItem>
                                    <SelectItem value="price-asc">
                                        Menor preço
                                    </SelectItem>
                                    <SelectItem value="price-desc">
                                        Maior preço
                                    </SelectItem>
                                    <SelectItem value="title-asc">
                                        A-Z
                                    </SelectItem>
                                    <SelectItem value="title-desc">
                                        Z-A
                                    </SelectItem>
                                </SelectContent>
                            </Select>

                            {/* Modo de visualização */}
                            <div className="flex rounded-md border">
                                <Button
                                    variant={
                                        viewMode === 'grid'
                                            ? 'default'
                                            : 'ghost'
                                    }
                                    size="sm"
                                    onClick={() => setViewMode('grid')}
                                    className="rounded-r-none"
                                >
                                    <Grid className="h-4 w-4" />
                                </Button>
                                <Button
                                    variant={
                                        viewMode === 'list'
                                            ? 'default'
                                            : 'ghost'
                                    }
                                    size="sm"
                                    onClick={() => setViewMode('list')}
                                    className="rounded-l-none"
                                >
                                    <List className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* Lista de anúncios */}
                    {advertisements?.data?.length > 0 ? (
                        <div
                            className={
                                viewMode === 'grid'
                                    ? 'grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                                    : 'space-y-3'
                            }
                        >
                            {advertisements.data.map((ad) => (
                                <Card
                                    key={ad.id}
                                    className="overflow-hidden border border-gray-200 bg-white transition-all hover:border-gray-300 hover:shadow-md"
                                >
                                    <div
                                        className={
                                            viewMode === 'grid' ? '' : 'flex'
                                        }
                                    >
                                        {/* Imagem */}
                                        <div
                                            className={
                                                viewMode === 'grid'
                                                    ? 'relative aspect-[4/3]'
                                                    : 'relative h-32 w-48 flex-shrink-0'
                                            }
                                        >
                                            {ad.featured_image ? (
                                                <img
                                                    src={ad.featured_image.url}
                                                    alt={ad.featured_image.alt}
                                                    className="h-full w-full object-cover"
                                                />
                                            ) : (
                                                <div className="flex h-full w-full items-center justify-center bg-gray-100">
                                                    <span className="text-sm text-gray-400">
                                                        Sem imagem
                                                    </span>
                                                </div>
                                            )}

                                            {/* Badges */}
                                            <div className="absolute top-2 left-2 flex gap-1">
                                                {ad.is_featured && (
                                                    <Badge className="bg-orange-500 text-xs text-white">
                                                        Destaque
                                                    </Badge>
                                                )}
                                            </div>

                                            {/* Botão de favorito */}
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="absolute top-2 right-2 h-8 w-8 bg-white/90 p-0 shadow-sm hover:bg-white"
                                            >
                                                <Heart className="h-4 w-4 text-gray-600" />
                                            </Button>
                                        </div>

                                        {/* Conteúdo */}
                                        <CardContent
                                            className={`p-3 ${viewMode === 'list' ? 'flex-1' : ''}`}
                                        >
                                            <div className="space-y-2">
                                                <h3 className="line-clamp-2 text-sm leading-tight font-medium">
                                                    <Link
                                                        href={`/anuncios/${ad.id}`}
                                                        className="text-gray-900 hover:text-blue-600"
                                                    >
                                                        {ad.title}
                                                    </Link>
                                                </h3>

                                                <div className="text-lg font-bold text-gray-900">
                                                    {formatPrice(ad.price)}
                                                </div>

                                                <div className="flex flex-wrap gap-1 text-xs text-gray-600">
                                                    <span>
                                                        {ad.vehicle.brand.name}
                                                    </span>
                                                    <span>•</span>
                                                    <span>
                                                        {ad.vehicle.year}
                                                    </span>
                                                    <span>•</span>
                                                    <span>
                                                        {formatMileage(
                                                            ad.vehicle.mileage,
                                                        )}
                                                    </span>
                                                </div>

                                                <div className="flex flex-wrap gap-1">
                                                    <Badge
                                                        variant="secondary"
                                                        className="px-2 py-0.5 text-xs"
                                                    >
                                                        {ad.vehicle.fuel_type}
                                                    </Badge>
                                                    <Badge
                                                        variant="secondary"
                                                        className="px-2 py-0.5 text-xs"
                                                    >
                                                        {
                                                            ad.vehicle
                                                                .transmission
                                                        }
                                                    </Badge>
                                                </div>

                                                <div className="flex items-center justify-between pt-1">
                                                    <div className="flex items-center text-xs text-gray-500">
                                                        <MapPin className="mr-1 h-3 w-3" />
                                                        {ad.location}
                                                    </div>
                                                    <div className="text-xs text-gray-400">
                                                        {formatDate(
                                                            ad.created_at,
                                                        )}
                                                    </div>
                                                </div>

                                                {/* Informações do vendedor */}
                                                <div className="mt-2 border-t pt-2">
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center gap-2">
                                                            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200">
                                                                <User className="h-3 w-3 text-gray-500" />
                                                            </div>
                                                            <span className="truncate text-xs text-gray-600">
                                                                {ad.user.name}
                                                            </span>
                                                        </div>
                                                        <Dialog>
                                                            <DialogTrigger
                                                                asChild
                                                            >
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="h-6 px-2 text-xs"
                                                                    onClick={() =>
                                                                        setSelectedAd(
                                                                            ad,
                                                                        )
                                                                    }
                                                                >
                                                                    <MessageCircle className="mr-1 h-3 w-3" />
                                                                    Chat
                                                                </Button>
                                                            </DialogTrigger>
                                                            <DialogContent className="sm:max-w-md">
                                                                <DialogHeader>
                                                                    <DialogTitle>
                                                                        Entrar
                                                                        em
                                                                        contato
                                                                    </DialogTitle>
                                                                </DialogHeader>
                                                                <form
                                                                    onSubmit={
                                                                        handleChatSubmit
                                                                    }
                                                                    className="space-y-4"
                                                                >
                                                                    <div>
                                                                        <p className="mb-2 text-sm text-gray-600">
                                                                            Envie
                                                                            uma
                                                                            mensagem
                                                                            para{' '}
                                                                            {
                                                                                ad
                                                                                    .user
                                                                                    .name
                                                                            }{' '}
                                                                            sobre:
                                                                        </p>
                                                                        <p className="text-sm font-medium">
                                                                            {
                                                                                ad.title
                                                                            }
                                                                        </p>
                                                                    </div>
                                                                    <div>
                                                                        <Textarea
                                                                            placeholder="Digite sua mensagem..."
                                                                            value={
                                                                                data.message
                                                                            }
                                                                            onChange={(
                                                                                e,
                                                                            ) =>
                                                                                setData(
                                                                                    'message',
                                                                                    e
                                                                                        .target
                                                                                        .value,
                                                                                )
                                                                            }
                                                                            className="min-h-[100px]"
                                                                        />
                                                                    </div>
                                                                    <div className="flex justify-end gap-2">
                                                                        <DialogTrigger
                                                                            asChild
                                                                        >
                                                                            <Button
                                                                                variant="outline"
                                                                                type="button"
                                                                            >
                                                                                Cancelar
                                                                            </Button>
                                                                        </DialogTrigger>
                                                                        <Button
                                                                            type="submit"
                                                                            disabled={
                                                                                processing ||
                                                                                !data.message.trim()
                                                                            }
                                                                        >
                                                                            Enviar
                                                                            mensagem
                                                                        </Button>
                                                                    </div>
                                                                </form>
                                                            </DialogContent>
                                                        </Dialog>
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </div>
                                </Card>
                            ))}
                        </div>
                    ) : (
                        <div className="py-12 text-center">
                            <h3 className="mb-2 text-xl font-semibold">
                                Nenhum anúncio encontrado
                            </h3>
                            <p className="mb-4 text-gray-600">
                                Tente fazer uma nova pesquisa ou ajustar os
                                filtros
                            </p>
                            <Link href="/pesquisar">
                                <Button>Pesquisar com filtros</Button>
                            </Link>
                        </div>
                    )}

                    {/* Paginação */}
                    {advertisements?.links &&
                        advertisements.links.length > 3 && (
                            <div className="mt-8 flex justify-center">
                                <div className="flex gap-2">
                                    {advertisements.links.map((link, index) => (
                                        <Button
                                            key={index}
                                            variant={
                                                link.active
                                                    ? 'default'
                                                    : 'outline'
                                            }
                                            size="sm"
                                            onClick={() => {
                                                if (link.url) {
                                                    router.get(link.url);
                                                }
                                            }}
                                            disabled={!link.url}
                                            dangerouslySetInnerHTML={{
                                                __html: link.label,
                                            }}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                </div>
            </div>
        </MainLayout>
    );
}
