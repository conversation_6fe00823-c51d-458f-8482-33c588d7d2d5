import ProductCard, { Part, Vehicle } from '@/components/ui/ProductCard';
import ProductCarousel from '@/components/ui/ProductCarousel';
import { Link } from '@inertiajs/react';
import { useState } from 'react';

interface FeaturedListingsProps {
    vehicles?: Vehicle[];
    parts?: Part[];
    title?: string;
    showMoreLink?: string;
}

export default function FeaturedListings({
    vehicles = [],
    parts = [],
    title = 'Destaques',
    showMoreLink,
}: FeaturedListingsProps) {
    const [favorites, setFavorites] = useState<Set<string>>(new Set());

    const toggleFavorite = (itemId: string) => {
        setFavorites((prev) => {
            const newFavorites = new Set(prev);
            if (newFavorites.has(itemId)) {
                newFavorites.delete(itemId);
            } else {
                newFavorites.add(itemId);
            }
            return newFavorites;
        });
    };

    const allItems = [...vehicles, ...parts];

    return (
        <section className="py-12">
            <div className="container mx-auto px-4">
                <div className="mb-8 flex items-center justify-between">
                    <h2 className="text-2xl font-semibold text-foreground">
                        {title}
                    </h2>
                    {showMoreLink && (
                        <Link
                            href={showMoreLink}
                            className="inline-flex items-center font-medium text-blue-600 hover:text-blue-800"
                        >
                            Ver todos
                            <svg
                                className="ml-1 h-4 w-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 5l7 7-7 7"
                                />
                            </svg>
                        </Link>
                    )}
                </div>

                <ProductCarousel
                    emptyMessage="Nenhum item em destaque no momento"
                    emptyIcon="📦"
                >
                    {allItems.map((item) => {
                        const productId = `${'model' in item ? 'vehicle' : 'part'}-${item.id}`;
                        return (
                            <ProductCard
                                key={productId}
                                product={item}
                                onToggleFavorite={toggleFavorite}
                                isFavorite={favorites.has(productId)}
                            />
                        );
                    })}
                </ProductCarousel>
            </div>
        </section>
    );
}
