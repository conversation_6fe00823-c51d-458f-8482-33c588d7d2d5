<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Advertisement extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia, HasFactory;

    protected $fillable = [
        'user_id',
        'vehicle_id',
        'title',
        'description',
        'status',
        'rejection_reason',
        'published_at',
        'expires_at',
        'is_featured',
        'price',
        'is_negotiable',
        'contact_phone',
        'contact_email',
        'location',
        'latitude',
        'longitude',
        'views'
    ];

    protected $casts = [
        'published_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_featured' => 'boolean',
        'is_negotiable' => 'boolean',
        'price' => 'decimal:2',
        'views' => 'integer',
    ];

    protected $appends = ['status_label', 'featured_image_url'];

    // Status constants
    public const STATUS_DRAFT = 'draft';
    public const STATUS_PENDING_REVIEW = 'pending_review';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';
    public const STATUS_PUBLISHED = 'published';
    public const STATUS_EXPIRED = 'expired';
    public const STATUS_SOLD = 'sold';

    /**
     * Get the user that owns the advertisement.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the vehicle associated with the advertisement.
     */
    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class);
    }

    /**
     * Get all of the advertisement's images.
     */
    public function images()
    {
        return $this->media()->where('collection_name', 'images');
    }

    /**
     * Get the featured image for the advertisement.
     */
    public function featuredImage()
    {
        return $this->media()->where('collection_name', 'featured_image');
    }

    /**
     * Get the featured image URL.
     */
    public function getFeaturedImageUrlAttribute()
    {
        $media = $this->getFirstMedia('featured_image');
        return $media ? $media->getUrl() : asset('images/default-vehicle.jpg');
    }

    /**
     * Get the status label.
     */
    public function getStatusLabelAttribute()
    {
        $statuses = [
            self::STATUS_DRAFT => 'Rascunho',
            self::STATUS_PENDING_REVIEW => 'Aguardando Aprovação',
            self::STATUS_APPROVED => 'Aprovado',
            self::STATUS_REJECTED => 'Rejeitado',
            self::STATUS_PUBLISHED => 'Publicado',
            self::STATUS_EXPIRED => 'Expirado',
            self::STATUS_SOLD => 'Vendido',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Scope a query to only include published advertisements.
     */
    public function scopePublished($query)
    {
        return $query->where('status', self::STATUS_PUBLISHED)
            ->where('expires_at', '>', now());
    }

    /**
     * Scope a query to only include featured advertisements.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include active advertisements.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', [self::STATUS_PUBLISHED, self::STATUS_APPROVED])
            ->where('expires_at', '>', now());
    }

    /**
     * Register the media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);

        $this->addMediaCollection('featured_image')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);
    }
}
